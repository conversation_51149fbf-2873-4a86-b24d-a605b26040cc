#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
from diffusers import UNet2DConditionModel

def check_unet_dimensions():
    """
    检查stable-diffusion-inpainting UNet的具体维度配置
    """
    print("🔍 检查UNet配置...")
    
    # 加载预训练模型
    model_path = "/home2/wangyuanpeng/.cache/huggingface/hub/models--runwayml--stable-diffusion-inpainting/snapshots/8a4288a76071f7280aedbdb3253bdb9e9d5d84bb"
    
    try:
        unet = UNet2DConditionModel.from_pretrained(model_path, subfolder="unet")
        
        print(f"📊 UNet配置信息:")
        print(f"  - block_out_channels: {unet.config.block_out_channels}")
        print(f"  - cross_attention_dim: {unet.config.cross_attention_dim}")
        print(f"  - attention_head_dim: {unet.config.attention_head_dim}")
        print(f"  - layers_per_block: {unet.config.layers_per_block}")
        print(f"  - down_block_types: {unet.config.down_block_types}")
        print(f"  - up_block_types: {unet.config.up_block_types}")
        
        print(f"\n🎯 Self-Attention (attn1) 维度分析:")
        
        # 分析每个block的hidden_size
        for name in unet.attn_processors.keys():
            if name.endswith("attn1.processor"):  # 只看self-attention
                if name.startswith("mid_block"):
                    hidden_size = unet.config.block_out_channels[-1]
                    block_type = "mid_block"
                elif name.startswith("up_blocks"):
                    block_id = int(name[len("up_blocks.")])
                    hidden_size = list(reversed(unet.config.block_out_channels))[block_id]
                    block_type = f"up_blocks.{block_id}"
                elif name.startswith("down_blocks"):
                    block_id = int(name[len("down_blocks.")])
                    hidden_size = unet.config.block_out_channels[block_id]
                    block_type = f"down_blocks.{block_id}"
                
                print(f"  - {name}: hidden_size={hidden_size} ({block_type})")
        
        print(f"\n📈 维度总结:")
        print(f"  - down_blocks维度: {unet.config.block_out_channels}")
        print(f"  - up_blocks维度: {list(reversed(unet.config.block_out_channels))}")
        print(f"  - mid_block维度: {unet.config.block_out_channels[-1]}")
        
        # 统计self-attention层数量
        attn1_count = sum(1 for name in unet.attn_processors.keys() if name.endswith("attn1.processor"))
        print(f"\n🔢 Self-Attention层数量: {attn1_count}")
        
        # 分析参数量
        total_params = sum(p.numel() for p in unet.parameters())
        attn1_params = 0
        
        for name, param in unet.named_parameters():
            if 'attn1' in name:
                attn1_params += param.numel()
        
        print(f"\n💾 参数量分析:")
        print(f"  - UNet总参数量: {total_params:,}")
        print(f"  - Self-Attention参数量: {attn1_params:,}")
        print(f"  - Self-Attention占比: {attn1_params/total_params*100:.2f}%")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        print("尝试使用在线模型...")
        
        try:
            unet = UNet2DConditionModel.from_pretrained("runwayml/stable-diffusion-inpainting", subfolder="unet")
            print(f"📊 UNet配置信息:")
            print(f"  - block_out_channels: {unet.config.block_out_channels}")
            print(f"  - cross_attention_dim: {unet.config.cross_attention_dim}")
            
        except Exception as e2:
            print(f"❌ 在线加载也失败: {e2}")

if __name__ == "__main__":
    check_unet_dimensions()
