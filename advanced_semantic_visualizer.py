#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级语义图可视化工具
专门处理已经分色的语义图像，自动识别不同颜色区域并生成注意力热图风格的可视化
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from PIL import Image, ImageDraw, ImageFont
import argparse
import os
from typing import Dict, List, Tuple, Optional
from collections import Counter

class AdvancedSemanticVisualizer:
    """高级语义图可视化器"""
    
    def __init__(self):
        # 预定义的语义区域名称
        self.region_names = {
            'region_1': '头发区域',
            'region_2': '面部区域', 
            'region_3': '颈部区域',
            'region_4': '上身区域',
            'region_5': '手臂区域',
            'region_6': '下身区域',
            'region_7': '腿部区域',
            'region_8': '背景区域'
        }
        
    def extract_color_regions(self, image_path: str, n_colors: int = 8) -> <PERSON><PERSON>[np.ndarray, List[Tuple], Dict]:
        """
        从分色图像中提取不同的颜色区域

        Args:
            image_path: 输入图像路径
            n_colors: 期望的颜色数量

        Returns:
            semantic_mask: 语义掩码
            dominant_colors: 主要颜色列表
            region_info: 区域信息字典
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")

        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        height, width = image_rgb.shape[:2]

        # 简单的颜色量化方法
        # 将RGB值量化到较少的级别
        quantized = (image_rgb // 32) * 32  # 将256级量化到8级

        # 找到唯一颜色
        pixels = quantized.reshape(-1, 3)
        unique_colors, inverse_indices, counts = np.unique(pixels, axis=0, return_inverse=True, return_counts=True)

        # 选择最常见的n_colors个颜色
        if len(unique_colors) > n_colors:
            # 按像素数量排序，选择前n_colors个
            sorted_indices = np.argsort(counts)[::-1][:n_colors]
            dominant_colors = unique_colors[sorted_indices]

            # 重新映射像素到选定的颜色
            semantic_mask = np.zeros((height, width), dtype=np.uint8)
            for i, color_idx in enumerate(sorted_indices):
                mask = np.all(quantized == unique_colors[color_idx], axis=2)
                semantic_mask[mask] = i
        else:
            dominant_colors = unique_colors
            semantic_mask = inverse_indices.reshape(height, width)

        # 计算每个区域的大小
        total_pixels = height * width
        region_info = {}
        for i, color in enumerate(dominant_colors):
            pixel_count = np.sum(semantic_mask == i)
            percentage = (pixel_count / total_pixels) * 100
            region_info[i] = {
                'color': tuple(color.astype(int)),
                'pixel_count': pixel_count,
                'percentage': percentage,
                'name': self.region_names.get(f'region_{i+1}', f'区域{i+1}')
            }

        return semantic_mask, dominant_colors, region_info, image_rgb
    
    def create_attention_heatmap(self, semantic_mask: np.ndarray, 
                               region_weights: Optional[Dict[int, float]] = None) -> np.ndarray:
        """
        创建注意力热图风格的可视化
        
        Args:
            semantic_mask: 语义掩码
            region_weights: 每个区域的注意力权重，如果为None则自动计算
        """
        height, width = semantic_mask.shape
        unique_labels = np.unique(semantic_mask)
        
        # 如果没有提供权重，则根据区域大小自动计算
        if region_weights is None:
            region_weights = {}
            for label in unique_labels:
                region_size = np.sum(semantic_mask == label)
                # 较小的区域给予更高的注意力权重
                region_weights[label] = 1.0 / (1.0 + region_size / (height * width))
        
        # 创建注意力热图
        attention_map = np.zeros((height, width), dtype=np.float32)
        
        for label in unique_labels:
            mask = semantic_mask == label
            weight = region_weights.get(label, 0.5)
            attention_map[mask] = weight
            
        # 归一化到0-1范围
        attention_map = (attention_map - attention_map.min()) / (attention_map.max() - attention_map.min())
        
        return attention_map
    
    def apply_colormap_to_heatmap(self, attention_map: np.ndarray, 
                                colormap: str = 'jet') -> np.ndarray:
        """
        将颜色映射应用到注意力热图
        
        Args:
            attention_map: 注意力热图
            colormap: matplotlib颜色映射名称
        """
        # 应用颜色映射
        cmap = plt.cm.get_cmap(colormap)
        colored_heatmap = cmap(attention_map)
        
        # 转换为0-255范围的RGB
        colored_heatmap = (colored_heatmap[:, :, :3] * 255).astype(np.uint8)
        
        return colored_heatmap
    
    def create_semantic_attention_overlay(self, original_image: np.ndarray,
                                        semantic_mask: np.ndarray,
                                        attention_map: np.ndarray,
                                        alpha: float = 0.6) -> np.ndarray:
        """
        创建语义注意力叠加图
        """
        height, width = semantic_mask.shape
        
        if original_image.shape[:2] != (height, width):
            original_image = cv2.resize(original_image, (width, height))
        
        # 创建彩色注意力图
        colored_attention = self.apply_colormap_to_heatmap(attention_map, 'jet')
        
        # 混合原图和注意力图
        overlay = cv2.addWeighted(original_image, 1-alpha, colored_attention, alpha, 0)
        
        return overlay
    
    def create_comprehensive_visualization(self, image_path: str,
                                         output_path: Optional[str] = None,
                                         n_colors: int = 8,
                                         alpha: float = 0.6) -> None:
        """
        创建综合的语义注意力可视化
        """
        # 提取颜色区域
        semantic_mask, dominant_colors, region_info, original_image = self.extract_color_regions(
            image_path, n_colors
        )
        
        # 创建注意力热图
        attention_map = self.create_attention_heatmap(semantic_mask)
        
        # 创建各种可视化
        colored_heatmap = self.apply_colormap_to_heatmap(attention_map, 'jet')
        overlay = self.create_semantic_attention_overlay(
            original_image, semantic_mask, attention_map, alpha
        )
        
        # 创建语义分割可视化
        semantic_colored = np.zeros_like(original_image)
        for i, color in enumerate(dominant_colors):
            mask = semantic_mask == i
            semantic_colored[mask] = color
        
        # 创建图表
        fig = plt.figure(figsize=(16, 12))
        
        # 原图
        plt.subplot(2, 3, 1)
        plt.imshow(original_image)
        plt.title('原始图像', fontsize=14)
        plt.axis('off')
        
        # 语义分割图
        plt.subplot(2, 3, 2)
        plt.imshow(semantic_colored)
        plt.title('语义分割图', fontsize=14)
        plt.axis('off')
        
        # 注意力热图
        plt.subplot(2, 3, 3)
        plt.imshow(colored_heatmap)
        plt.title('注意力热图', fontsize=14)
        plt.axis('off')
        
        # 叠加图
        plt.subplot(2, 3, 4)
        plt.imshow(overlay)
        plt.title(f'注意力叠加图 (α={alpha})', fontsize=14)
        plt.axis('off')
        
        # 区域统计
        plt.subplot(2, 3, 5)
        regions = list(region_info.keys())
        percentages = [region_info[r]['percentage'] for r in regions]
        colors_norm = [np.array(region_info[r]['color'])/255.0 for r in regions]
        
        bars = plt.bar(range(len(regions)), percentages, color=colors_norm)
        plt.title('区域面积占比', fontsize=14)
        plt.xlabel('区域ID')
        plt.ylabel('面积占比 (%)')
        plt.xticks(range(len(regions)), [f'区域{r}' for r in regions], rotation=45)
        
        # 添加数值标签
        for bar, pct in zip(bars, percentages):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{pct:.1f}%', ha='center', va='bottom', fontsize=10)
        
        # 颜色图例
        plt.subplot(2, 3, 6)
        legend_img = np.ones((200, 300, 3), dtype=np.uint8) * 255
        legend_pil = Image.fromarray(legend_img)
        draw = ImageDraw.Draw(legend_pil)
        
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
        except:
            font = ImageFont.load_default()
        
        y_offset = 10
        for region_id, info in region_info.items():
            color = info['color']
            name = info['name']
            percentage = info['percentage']
            
            # 绘制颜色块
            draw.rectangle([10, y_offset, 40, y_offset + 15], fill=color)
            
            # 绘制文字
            text = f"{name}: {percentage:.1f}%"
            draw.text((50, y_offset), text, fill=(0, 0, 0), font=font)
            
            y_offset += 20
        
        plt.imshow(np.array(legend_pil))
        plt.title('语义区域图例', fontsize=14)
        plt.axis('off')
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"综合可视化结果已保存到: {output_path}")
        else:
            plt.show()
            
        plt.close()
        
        # 打印区域信息
        print("\n=== 语义区域分析 ===")
        for region_id, info in sorted(region_info.items(), key=lambda x: x[1]['percentage'], reverse=True):
            print(f"区域 {region_id}: {info['name']}")
            print(f"  颜色: RGB{info['color']}")
            print(f"  像素数: {info['pixel_count']:,}")
            print(f"  面积占比: {info['percentage']:.2f}%")
            print()

def main():
    parser = argparse.ArgumentParser(description='高级语义图可视化工具')
    parser.add_argument('--input', '-i', type=str, required=True,
                       help='输入图像路径')
    parser.add_argument('--output', '-o', type=str, default=None,
                       help='输出图像路径')
    parser.add_argument('--colors', '-c', type=int, default=8,
                       help='期望的颜色区域数量')
    parser.add_argument('--alpha', '-a', type=float, default=0.6,
                       help='注意力图透明度 (0-1)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return
    
    # 创建可视化器
    visualizer = AdvancedSemanticVisualizer()
    
    # 生成综合可视化
    visualizer.create_comprehensive_visualization(
        image_path=args.input,
        output_path=args.output,
        n_colors=args.colors,
        alpha=args.alpha
    )

if __name__ == "__main__":
    main()
