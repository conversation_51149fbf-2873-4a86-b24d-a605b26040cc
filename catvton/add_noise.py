import torch
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
from diffusers import DDPMScheduler
import os

# 你的图像路径
image_path = "/home2/wangyuanpeng/project/VTON/amymodel/lem/IMAGGarment-1-main/catvton/456F81B0FDFE9620D9F24D1A3AFC12D8.png"

class VTONNoiseSimulator:
    def __init__(self, num_train_timesteps=1000):
        # 兼容不同版本的diffusers
        try:
            self.scheduler = DDPMScheduler(
                num_train_timesteps=num_train_timesteps,
                beta_start=0.00085,
                beta_end=0.012,
                beta_schedule="scaled_linear",
                clip_sample=False,
                set_alpha_to_one=False,
            )
        except TypeError:
            # 旧版本diffusers不支持set_alpha_to_one参数
            self.scheduler = DDPMScheduler(
                num_train_timesteps=num_train_timesteps,
                beta_start=0.00085,
                beta_end=0.012,
                beta_schedule="scaled_linear",
                clip_sample=False,
            )
        
        self.transform = transforms.Compose([
            transforms.Resize((512, 384)),
            transforms.ToTensor(),
            transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
        ])
        
        self.denormalize = transforms.Compose([
            transforms.Normalize([-1, -1, -1], [2, 2, 2]),
            transforms.ToPILImage()
        ])

    def add_noise_to_image(self, image_path, timestep=200):
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return None, None, None
            
        # 加载图像
        try:
            image = Image.open(image_path).convert('RGB')
            print(f"✅ 成功加载图像，尺寸: {image.size}")
        except Exception as e:
            print(f"❌ 加载图像失败: {e}")
            return None, None, None
        
        # 预处理
        image_tensor = self.transform(image).unsqueeze(0)
        
        # 生成噪声
        noise = torch.randn_like(image_tensor)
        
        # 添加噪声
        timesteps = torch.tensor([timestep])
        noisy_image = self.scheduler.add_noise(image_tensor, noise, timesteps)
        
        # 转换回PIL
        noisy_pil = self.denormalize(noisy_image.squeeze(0))
        original_pil = self.denormalize(image_tensor.squeeze(0))
        
        return original_pil, noisy_pil, noise

    def create_noise_comparison(self, image_path, timesteps=[50, 150, 300, 500, 800]):
        """创建不同噪声级别的对比图"""
        
        # 检查文件
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return None
            
        # 加载原图
        try:
            original_image = Image.open(image_path).convert('RGB')
            print(f"✅ 原图尺寸: {original_image.size}")
        except Exception as e:
            print(f"❌ 加载图像失败: {e}")
            return None
            
        image_tensor = self.transform(original_image).unsqueeze(0)
        
        # 创建对比图
        fig, axes = plt.subplots(2, len(timesteps) + 1, figsize=(18, 8))
        
        # 第一行第一列：原图
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title('Original Image', fontsize=12, fontweight='bold')
        axes[0, 0].axis('off')
        
        # 第二行第一列：预处理后的图
        processed_original = self.denormalize(image_tensor.squeeze(0))
        axes[1, 0].imshow(processed_original)
        axes[1, 0].set_title('Processed Original', fontsize=12)
        axes[1, 0].axis('off')
        
        # 添加不同程度的噪声
        for i, t in enumerate(timesteps):
            col = i + 1
            
            # 生成噪声
            noise = torch.randn_like(image_tensor)
            timestep_tensor = torch.tensor([t])
            noisy_image = self.scheduler.add_noise(image_tensor, noise, timestep_tensor)
            noisy_pil = self.denormalize(noisy_image.squeeze(0))
            
            # 第一行：加噪声的图像
            axes[0, col].imshow(noisy_pil)
            axes[0, col].set_title(f'Timestep {t}\n({t/1000:.1%} noise)', fontsize=10)
            axes[0, col].axis('off')
            
            # 第二行：噪声可视化
            noise_vis = (noise.squeeze(0).permute(1, 2, 0) + 1) / 2
            noise_vis = torch.clamp(noise_vis, 0, 1)
            axes[1, col].imshow(noise_vis)
            axes[1, col].set_title(f'Noise Pattern', fontsize=10)
            axes[1, col].axis('off')
            
            # 保存单独的加噪图像
            output_path = f"noisy_image_t{t}.png"
            noisy_pil.save(output_path)
            print(f"💾 保存: {output_path}")
        
        plt.suptitle('VTON Noise Simulation - Different Timesteps', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存对比网格
        grid_path = 'noise_comparison_grid.png'
        plt.savefig(grid_path, dpi=150, bbox_inches='tight')
        print(f"💾 保存对比网格: {grid_path}")
        
        # 显示图像
        plt.show()
        
        return fig

def main():
    print("🎯 开始为你的图像添加噪声...")
    print(f"📁 图像路径: {image_path}")
    
    # 创建模拟器
    simulator = VTONNoiseSimulator()
    
    # 创建噪声对比图
    fig = simulator.create_noise_comparison(image_path)
    
    if fig is not None:
        print("\n✅ 噪声对比图已生成！")
        print("📁 输出文件:")
        print("  - noise_comparison_grid.png (完整对比网格)")
        print("  - noisy_image_t50.png (轻微噪声 - 5%)")
        print("  - noisy_image_t150.png (中等噪声 - 15%)")
        print("  - noisy_image_t300.png (较强噪声 - 30%)")
        print("  - noisy_image_t500.png (强噪声 - 50%)")
        print("  - noisy_image_t800.png (很强噪声 - 80%)")
        
        # 生成一个推荐的中等强度版本
        print("\n🎨 生成推荐的中等强度噪声版本...")
        original, noisy, _ = simulator.add_noise_to_image(image_path, 250)
        if noisy is not None:
            noisy.save("recommended_noisy_t250.png")
            print("💾 保存推荐版本: recommended_noisy_t250.png")
            
            # 创建简单对比
            fig2, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 6))
            ax1.imshow(original)
            ax1.set_title('Original (Processed)', fontsize=14)
            ax1.axis('off')
            
            ax2.imshow(noisy)
            ax2.set_title('With Noise (t=250)', fontsize=14)
            ax2.axis('off')
            
            plt.suptitle('推荐的噪声强度对比', fontsize=16)
            plt.tight_layout()
            plt.savefig('simple_comparison.png', dpi=150, bbox_inches='tight')
            plt.show()
            print("💾 保存简单对比: simple_comparison.png")
    else:
        print("❌ 生成失败，请检查图像路径")

if __name__ == "__main__":
    main()
