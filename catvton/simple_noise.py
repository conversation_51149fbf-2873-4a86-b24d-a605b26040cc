import torch
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
import os

# 你的图像路径
image_path = "/home2/wangyuanpeng/project/VTON/amymodel/lem/IMAGGarment-1-main/catvton/456F81B0FDFE9620D9F24D1A3AFC12D8.png"

class SimpleNoiseAdder:
    def __init__(self):
        self.transform = transforms.Compose([
            transforms.Resize((512, 384)),
            transforms.ToTensor(),
            transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])  # [-1, 1]
        ])
        
        self.denormalize = transforms.Compose([
            transforms.Normalize([-1, -1, -1], [2, 2, 2]),  # [0, 1]
            transforms.ToPILImage()
        ])

    def add_simple_noise(self, image_path, noise_level=0.3):
        """
        简单的噪声添加方法
        noise_level: 0.0-1.0, 噪声强度
        """
        # 检查文件
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return None, None
            
        # 加载图像
        try:
            image = Image.open(image_path).convert('RGB')
            print(f"✅ 成功加载图像，尺寸: {image.size}")
        except Exception as e:
            print(f"❌ 加载图像失败: {e}")
            return None, None
        
        # 预处理
        image_tensor = self.transform(image).unsqueeze(0)
        
        # 生成噪声
        noise = torch.randn_like(image_tensor)
        
        # 简单的线性混合添加噪声
        noisy_image = (1 - noise_level) * image_tensor + noise_level * noise
        
        # 转换回PIL
        original_pil = self.denormalize(image_tensor.squeeze(0))
        noisy_pil = self.denormalize(noisy_image.squeeze(0))
        
        return original_pil, noisy_pil

    def create_noise_levels(self, image_path, noise_levels=[0.1, 0.2, 0.3, 0.5, 0.8]):
        """创建不同噪声级别的对比"""
        
        # 检查文件
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return None
            
        # 加载原图
        try:
            original_image = Image.open(image_path).convert('RGB')
            print(f"✅ 原图尺寸: {original_image.size}")
        except Exception as e:
            print(f"❌ 加载图像失败: {e}")
            return None
        
        # 创建对比图
        fig, axes = plt.subplots(2, len(noise_levels) + 1, figsize=(18, 8))
        
        # 第一行第一列：原图
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title('Original Image', fontsize=12, fontweight='bold')
        axes[0, 0].axis('off')
        
        # 第二行第一列：处理后的原图
        original_processed, _ = self.add_simple_noise(image_path, 0.0)
        axes[1, 0].imshow(original_processed)
        axes[1, 0].set_title('Processed Original', fontsize=12)
        axes[1, 0].axis('off')
        
        # 添加不同程度的噪声
        for i, noise_level in enumerate(noise_levels):
            col = i + 1
            
            # 生成噪声图像
            original, noisy = self.add_simple_noise(image_path, noise_level)
            
            if noisy is not None:
                # 第一行：加噪声的图像
                axes[0, col].imshow(noisy)
                axes[0, col].set_title(f'Noise Level {noise_level}\n({noise_level*100:.0f}% noise)', fontsize=10)
                axes[0, col].axis('off')
                
                # 保存单独的图像
                output_path = f"simple_noisy_{noise_level:.1f}.png"
                noisy.save(output_path)
                print(f"💾 保存: {output_path}")
                
                # 第二行：噪声强度信息
                axes[1, col].text(0.5, 0.5, f'Noise: {noise_level:.1f}\nStrength: {noise_level*100:.0f}%', 
                                ha='center', va='center', fontsize=12,
                                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
                axes[1, col].axis('off')
        
        plt.suptitle('Simple Noise Addition - Different Levels', fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        # 保存对比网格
        grid_path = 'simple_noise_comparison.png'
        plt.savefig(grid_path, dpi=150, bbox_inches='tight')
        print(f"💾 保存对比网格: {grid_path}")
        
        # 显示图像
        plt.show()
        
        return fig

def create_diffusion_style_noise(image_path, timesteps=[50, 200, 400, 600, 900]):
    """
    模拟扩散模型风格的噪声添加
    使用数学公式而不是diffusers库
    """
    # 检查文件
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return None
        
    # 加载图像
    try:
        image = Image.open(image_path).convert('RGB')
        print(f"✅ 原图尺寸: {image.size}")
    except Exception as e:
        print(f"❌ 加载图像失败: {e}")
        return None
    
    # 预处理
    transform = transforms.Compose([
        transforms.Resize((512, 384)),
        transforms.ToTensor(),
        transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
    ])
    
    denormalize = transforms.Compose([
        transforms.Normalize([-1, -1, -1], [2, 2, 2]),
        transforms.ToPILImage()
    ])
    
    image_tensor = transform(image).unsqueeze(0)
    
    # 创建对比图
    fig, axes = plt.subplots(2, len(timesteps) + 1, figsize=(18, 8))
    
    # 原图
    axes[0, 0].imshow(image)
    axes[0, 0].set_title('Original', fontsize=12, fontweight='bold')
    axes[0, 0].axis('off')
    
    processed_original = denormalize(image_tensor.squeeze(0))
    axes[1, 0].imshow(processed_original)
    axes[1, 0].set_title('Processed', fontsize=12)
    axes[1, 0].axis('off')
    
    # 扩散风格的噪声参数
    max_timesteps = 1000
    
    for i, t in enumerate(timesteps):
        col = i + 1
        
        # 计算噪声比例 (模拟扩散过程)
        alpha = 1 - (t / max_timesteps)  # 信号保留比例
        noise_ratio = np.sqrt(1 - alpha**2)  # 噪声比例
        
        # 生成噪声
        noise = torch.randn_like(image_tensor)
        
        # 混合信号和噪声
        noisy_image = alpha * image_tensor + noise_ratio * noise
        
        # 转换为PIL
        noisy_pil = denormalize(noisy_image.squeeze(0))
        
        # 显示
        axes[0, col].imshow(noisy_pil)
        axes[0, col].set_title(f'Timestep {t}\nSignal: {alpha:.2f}', fontsize=10)
        axes[0, col].axis('off')
        
        # 噪声可视化
        noise_vis = (noise.squeeze(0).permute(1, 2, 0) + 1) / 2
        noise_vis = torch.clamp(noise_vis, 0, 1)
        axes[1, col].imshow(noise_vis)
        axes[1, col].set_title(f'Noise: {noise_ratio:.2f}', fontsize=10)
        axes[1, col].axis('off')
        
        # 保存
        output_path = f"diffusion_style_t{t}.png"
        noisy_pil.save(output_path)
        print(f"💾 保存: {output_path}")
    
    plt.suptitle('Diffusion-Style Noise Addition', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('diffusion_style_comparison.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return fig

def main():
    print("🎯 开始为你的图像添加噪声...")
    print(f"📁 图像路径: {image_path}")
    
    # 方法1: 简单噪声添加
    print("\n📊 方法1: 简单线性噪声添加")
    noise_adder = SimpleNoiseAdder()
    fig1 = noise_adder.create_noise_levels(image_path)
    
    # 方法2: 扩散风格噪声
    print("\n📊 方法2: 扩散风格噪声添加")
    fig2 = create_diffusion_style_noise(image_path)
    
    if fig1 is not None or fig2 is not None:
        print("\n✅ 噪声图像已生成！")
        print("📁 输出文件:")
        print("  简单噪声:")
        print("    - simple_noise_comparison.png (对比网格)")
        print("    - simple_noisy_0.1.png 到 simple_noisy_0.8.png")
        print("  扩散风格噪声:")
        print("    - diffusion_style_comparison.png (对比网格)")
        print("    - diffusion_style_t50.png 到 diffusion_style_t900.png")
    else:
        print("❌ 生成失败，请检查图像路径")

if __name__ == "__main__":
    main()
