---
name: "\U0001F680Feature Request"
about: Submit a proposal/request for a new detectron2 feature

---

## 🚀 Feature
A clear and concise description of the feature proposal.


## Motivation & Examples

Tell us why the feature is useful.

Describe what the feature would look like, if it is implemented.
Best demonstrated using **code examples** in addition to words.

## Note

We only consider adding new features if they are relevant to many users.

If you request implementation of research papers --
we only consider papers that have enough significance and prevalance in the object detection field.

We do not take requests for most projects in the `projects/` directory,
because they are research code release that is mainly for other researchers to reproduce results.

Instead of adding features inside detectron2,
you can implement many features by [extending detectron2](https://detectron2.readthedocs.io/tutorials/extend.html).
The [projects/](https://github.com/facebookresearch/detectron2/tree/master/projects/) directory contains many of such examples.

