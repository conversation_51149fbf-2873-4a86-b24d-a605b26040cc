# Read the docs:

The latest documentation built from this directory is available at [detectron2.readthedocs.io](https://detectron2.readthedocs.io/).
Documents in this directory are not meant to be read on github.

# Build the docs:

1. Install detectron2 according to [INSTALL.md](INSTALL.md).
2. Install additional libraries required to build docs:
  - docutils==0.16
  - Sphinx==3.0.0
  - recommonmark==0.6.0
  - sphinx_rtd_theme
  - mock

3. Run `make html` from this directory.
