_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  WEIGHTS: "detectron2://ImageNetPretrained/FAIR/X-101-32x8d.pkl"
  PIXEL_STD: [57.375, 57.120, 58.395]
  MASK_ON: True
  RESNETS:
    STRIDE_IN_1X1: False  # this is a C2 model
    NUM_GROUPS: 32
    WIDTH_PER_GROUP: 8
    DEPTH: 101
  ROI_HEADS:
    NUM_CLASSES: 1230
    SCORE_THRESH_TEST: 0.0001
INPUT:
  MIN_SIZE_TRAIN: (640, 672, 704, 736, 768, 800)
DATASETS:
  TRAIN: ("lvis_v0.5_train",)
  TEST: ("lvis_v0.5_val",)
TEST:
  DETECTIONS_PER_IMAGE: 300  # LVIS allows up to 300
DATALOADER:
  SAMPLER_TRAIN: "RepeatFactorTrainingSampler"
  REPEAT_THRESHOLD: 0.001
