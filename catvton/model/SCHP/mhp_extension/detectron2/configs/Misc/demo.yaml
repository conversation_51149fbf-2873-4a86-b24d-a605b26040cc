_BASE_: "cascade_mask_rcnn_X_152_32x8d_FPN_IN5k_gn_dconv.yaml"
MODEL:
  MASK_ON: True
  ROI_HEADS:
    NMS_THRESH_TEST: 0.95
    SCORE_THRESH_TEST: 0.5
    NUM_CLASSES: 1
SOLVER:
  IMS_PER_BATCH: 1
  STEPS: (30000, 45000)
  MAX_ITER: 50000
  BASE_LR: 0.02
INPUT:
  MIN_SIZE_TRAIN: (640, 864)
  MIN_SIZE_TRAIN_SAMPLING: "range"
  MAX_SIZE_TRAIN: 1440
  CROP:
    ENABLED: True
TEST:
  AUG:
    ENABLED: True
DATASETS:
  TRAIN: ("demo_train",)
  TEST: ("demo_val",)
OUTPUT_DIR: "../../data/DemoDataset/detectron2_prediction"
