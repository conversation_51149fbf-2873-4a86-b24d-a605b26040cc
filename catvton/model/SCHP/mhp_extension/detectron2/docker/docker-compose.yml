version: "2.3"
services:
  detectron2:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        USER_ID: ${USER_ID:-1000}
    runtime: nvidia  # TODO: Exchange with "gpu: all" in the future (see https://github.com/facebookresearch/detectron2/pull/197/commits/00545e1f376918db4a8ce264d427a07c1e896c5a).
    shm_size: "8gb"
    ulimits:
      memlock: -1
      stack: 67108864
    volumes:
      - /tmp/.X11-unix:/tmp/.X11-unix:ro
    environment:
      - DISPLAY=$DISPLAY
      - NVIDIA_VISIBLE_DEVICES=all
