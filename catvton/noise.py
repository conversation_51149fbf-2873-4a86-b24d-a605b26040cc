import torch
import torchvision.transforms as transforms
from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
from diffusers import DDPMScheduler
import argparse
import os

class VTONNoiseSimulator:
    def __init__(self, num_train_timesteps=1000):
        """
        模拟VTON训练时的噪声添加过程
        """
        self.scheduler = DDPMScheduler(
            num_train_timesteps=num_train_timesteps,
            beta_start=0.00085,
            beta_end=0.012,
            beta_schedule="scaled_linear",
            clip_sample=False,
            set_alpha_to_one=False,
        )

        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((512, 384)),  # VTON常用尺寸
            transforms.ToTensor(),
            transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])  # [-1, 1]
        ])

        # 反归一化用于显示
        self.denormalize = transforms.Compose([
            transforms.Normalize([-1, -1, -1], [2, 2, 2]),  # [0, 1]
            transforms.ToPILImage()
        ])

    def add_noise_to_image(self, image_path, timestep=200, save_path=None):
        """
        为图像添加指定时间步的噪声
        
        Args:
            image_path: 输入图像路径
            timestep: 噪声时间步 (0-1000, 越大噪声越多)
            save_path: 保存路径
        """
        # 加载和预处理图像
        image = Image.open(image_path).convert('RGB')
        image_tensor = self.transform(image).unsqueeze(0)  # [1, 3, H, W]
        
        # 生成随机噪声
        noise = torch.randn_like(image_tensor)
        
        # 添加噪声
        timesteps = torch.tensor([timestep])
        noisy_image = self.scheduler.add_noise(image_tensor, noise, timesteps)
        
        # 转换回PIL图像
        noisy_pil = self.denormalize(noisy_image.squeeze(0))
        
        if save_path:
            noisy_pil.save(save_path)
            
        return noisy_pil, image_tensor, noise

    def simulate_training_conditions(self, person_path, cloth_path, mask_path=None):
        """
        模拟VTON训练时的完整条件输入
        """
        # 加载图像
        person_img = Image.open(person_path).convert('RGB')
        cloth_img = Image.open(cloth_path).convert('RGB')
        
        # 预处理
        person_tensor = self.transform(person_img).unsqueeze(0)
        cloth_tensor = self.transform(cloth_img).unsqueeze(0)
        
        # 创建简单遮罩（如果没有提供）
        if mask_path is None:
            # 创建上半身遮罩
            mask = torch.zeros(1, 1, 512, 384)
            mask[:, :, 100:400, :] = 1.0  # 上半身区域
        else:
            mask_img = Image.open(mask_path).convert('L')
            mask_transform = transforms.Compose([
                transforms.Resize((512, 384)),
                transforms.ToTensor()
            ])
            mask = mask_transform(mask_img).unsqueeze(0)
        
        # 应用遮罩
        masked_person = person_tensor * (mask < 0.5)
        
        # 模拟Y轴拼接
        condition_tensor = torch.cat([masked_person, cloth_tensor], dim=-2)  # 高度方向拼接
        
        # 添加不同程度的噪声
        timesteps = [50, 150, 300, 500, 800]  # 不同噪声级别
        results = {}
        
        for t in timesteps:
            noise = torch.randn_like(condition_tensor)
            timestep_tensor = torch.tensor([t])
            noisy_condition = self.scheduler.add_noise(condition_tensor, noise, timestep_tensor)
            
            # 分离人物和服装部分
            h = person_tensor.shape[-2]
            noisy_person = noisy_condition[:, :, :h, :]
            noisy_cloth = noisy_condition[:, :, h:, :]
            
            results[f't_{t}'] = {
                'noisy_person': self.denormalize(noisy_person.squeeze(0)),
                'noisy_cloth': self.denormalize(noisy_cloth.squeeze(0)),
                'original_person': self.denormalize(person_tensor.squeeze(0)),
                'original_cloth': self.denormalize(cloth_tensor.squeeze(0)),
                'masked_person': self.denormalize(masked_person.squeeze(0))
            }
        
        return results

    def create_comparison_grid(self, results, save_path='noise_comparison.png'):
        """
        创建噪声对比网格图
        """
        timesteps = sorted([int(k.split('_')[1]) for k in results.keys()])
        
        fig, axes = plt.subplots(3, len(timesteps) + 1, figsize=(20, 12))
        
        # 第一列：原图
        axes[0, 0].imshow(results[f't_{timesteps[0]}']['original_person'])
        axes[0, 0].set_title('Original Person', fontsize=12)
        axes[0, 0].axis('off')
        
        axes[1, 0].imshow(results[f't_{timesteps[0]}']['original_cloth'])
        axes[1, 0].set_title('Original Cloth', fontsize=12)
        axes[1, 0].axis('off')
        
        axes[2, 0].imshow(results[f't_{timesteps[0]}']['masked_person'])
        axes[2, 0].set_title('Masked Person', fontsize=12)
        axes[2, 0].axis('off')
        
        # 其他列：不同噪声级别
        for i, t in enumerate(timesteps):
            col = i + 1
            result = results[f't_{t}']
            
            axes[0, col].imshow(result['noisy_person'])
            axes[0, col].set_title(f'Noisy Person (t={t})', fontsize=10)
            axes[0, col].axis('off')
            
            axes[1, col].imshow(result['noisy_cloth'])
            axes[1, col].set_title(f'Noisy Cloth (t={t})', fontsize=10)
            axes[1, col].axis('off')
            
            # 第三行显示噪声强度信息
            axes[2, col].text(0.5, 0.5, f'Timestep: {t}\nNoise Level: {t/1000:.1%}', 
                            ha='center', va='center', fontsize=10,
                            bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
            axes[2, col].axis('off')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='VTON Noise Simulation')
    parser.add_argument('--person', type=str, required=True, help='Person image path')
    parser.add_argument('--cloth', type=str, required=True, help='Cloth image path')
    parser.add_argument('--mask', type=str, default=None, help='Mask image path (optional)')
    parser.add_argument('--timestep', type=int, default=200, help='Noise timestep (0-1000)')
    parser.add_argument('--output', type=str, default='noisy_output.png', help='Output path')
    parser.add_argument('--mode', type=str, choices=['single', 'comparison'], default='comparison',
                       help='Single image or comparison grid')
    
    args = parser.parse_args()
    
    simulator = VTONNoiseSimulator()
    
    if args.mode == 'single':
        # 单张图片加噪声
        noisy_img, _, _ = simulator.add_noise_to_image(args.person, args.timestep, args.output)
        print(f"Noisy image saved to {args.output}")
        
    else:
        # 完整的训练条件模拟
        results = simulator.simulate_training_conditions(args.person, args.cloth, args.mask)
        simulator.create_comparison_grid(results, args.output)
        print(f"Comparison grid saved to {args.output}")

# 简化版本的直接使用脚本
def quick_add_noise(image_path, timestep=200, output_path='noisy_image.png'):
    """
    快速为单张图片添加噪声
    """
    simulator = VTONNoiseSimulator()
    noisy_img, _, _ = simulator.add_noise_to_image(image_path, timestep, output_path)
    return noisy_img

if __name__ == "__main__":
    # 示例用法
    print("VTON Noise Simulation Script")
    print("Usage examples:")
    print("1. Single image: python script.py --person person.jpg --cloth cloth.jpg --mode single --timestep 300")
    print("2. Comparison: python script.py --person person.jpg --cloth cloth.jpg --mode comparison")
    print("3. Quick function: quick_add_noise('your_image.jpg', timestep=250)")
    
    # 如果直接运行，使用示例
    try:
        # 替换为你的图片路径
        person_path = "person_image.jpg"  # 你的人物图片
        cloth_path = "cloth_image.jpg"    # 你的服装图片
        
        simulator = VTONNoiseSimulator()
        
        # 生成不同噪声级别的对比
        results = simulator.simulate_training_conditions(person_path, cloth_path)
        simulator.create_comparison_grid(results, 'vton_noise_comparison.png')
        
        print("Noise comparison generated successfully!")
        
    except FileNotFoundError:
        print("Please provide valid image paths or use command line arguments")
        main()