#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import itertools
import logging
import math
import os
import random
import shutil
import warnings
from pathlib import Path

import numpy as np
import torch
import torch.nn.functional as F
import torch.utils.checkpoint
import transformers
from accelerate import Accelerator
from accelerate.logging import get_logger
from accelerate.utils import ProjectConfiguration, set_seed
from packaging import version
from PIL import Image
from tqdm.auto import tqdm
from transformers import CLIPImageProcessor, CLIPVisionModel

import diffusers
from diffusers import AutoencoderKL, DDPMScheduler, StableDiffusionPipeline, UNet2DConditionModel
from diffusers.utils import is_wandb_available

from adapter.attention_processor import AttnProcessor2_0, SkipAttnProcessor
from utils import *
from VTON_LEM_dataset_CatVTON_style import VTONLEMDatasetCatVTONStyle, collate_fn

# 如果可用，将会设置一些加速器
if is_wandb_available():
    import wandb

# 抑制部分warnings
warnings.filterwarnings("ignore")

logger = get_logger(__name__, log_level="INFO")


def save_catvton_format_weights(attn_blocks, save_path, logger):
    """
    保存CatVTON格式的attention权重
    将attn_block.X.to_q.weight格式转换为X.to_q.weight格式
    """
    import os
    from safetensors.torch import save_file

    # 创建CatVTON格式的权重字典
    catvton_dict = {}

    # 遍历所有attention block参数
    for name, param in attn_blocks.named_parameters():
        # name格式: "0.to_q.weight", "0.to_k.weight", "0.to_v.weight", "0.to_out.0.weight", "0.to_out.0.bias"
        # 或者: "8.to_q.weight", "104.to_k.weight" 等

        # 直接使用原始名称（去掉可能的前缀）
        clean_name = name
        if clean_name.startswith('module.'):
            clean_name = clean_name[7:]  # 去掉 'module.' 前缀

        catvton_dict[clean_name] = param.data.cpu()

    # 保存为safetensors格式（与CatVTON一致）
    safetensors_path = os.path.join(save_path, "catvton_attention_weights.safetensors")
    save_file(catvton_dict, safetensors_path)

    # 也保存为pth格式（兼容性）
    pth_path = os.path.join(save_path, "attn_blocks.pth")
    legacy_dict = {}
    for name, param in attn_blocks.named_parameters():
        legacy_dict[f"attn_block.{name}"] = param.data.cpu()
    torch.save(legacy_dict, pth_path)

    logger.info(f"Saved CatVTON format weights: {safetensors_path}")
    logger.info(f"Saved legacy format weights: {pth_path}")
    logger.info(f"Total attention parameters: {len(catvton_dict)}")


class LEM(torch.nn.Module):
    """
    Local Enhancement Model for Virtual Try-On
    """
    def __init__(self, unet, attn_block, ckpt_path=None):
        super(LEM, self).__init__()
        self.unet = unet
        self.attn_block = attn_block
        
        if ckpt_path:
            self.load_from_checkpoint(ckpt_path)

    def forward(self, noisy_latents, timesteps):
        # Predict the noise residual
        noise_pred = self.unet(noisy_latents, timesteps, encoder_hidden_states=None).sample
        return noise_pred

    def load_from_checkpoint(self, ckpt_path: str):
        # Calculate original checksums
        sd_state = torch.load(ckpt_path, map_location="cpu", weights_only=True)
        attn_sd = {}
        for k in sd_state:
            if 'attn_block' in k:
                attn_sd[k.replace('attn_block.', '')] = sd_state[k]
        self.attn_block.load_state_dict(attn_sd)


def check_inputs(image, condition_image, mask, width, height):
    """
    检查和调整输入图像的尺寸
    """
    assert image.shape[-1] == width and image.shape[-2] == height
    assert condition_image.shape[-1] == width and condition_image.shape[-2] == height
    assert mask.shape[-1] == width and mask.shape[-2] == height
    return image, condition_image, mask


def parse_args():
    parser = argparse.ArgumentParser(description="Simple example of a training script for LEM VTON.")
    parser.add_argument(
        "--pretrained_model_name_or_path",
        type=str,
        default="runwayml/stable-diffusion-inpainting",
        help="Path to pretrained model or model identifier from huggingface.co/models.",
    )
    parser.add_argument(
        "--data_root_path",
        type=str,
        default="",
        required=True,
        help="VITON-HD dataset root path",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="output_vton",
        help="The output directory where the model predictions and checkpoints will be written.",
    )
    parser.add_argument(
        "--logging_dir",
        type=str,
        default="logs",
        help="TensorBoard log directory.",
    )
    parser.add_argument(
        "--height",
        type=int,
        default=512,
        help="The height for input images",
    )
    parser.add_argument(
        "--width",
        type=int,
        default=384,
        help="The width for input images",
    )
    parser.add_argument(
        "--learning_rate",
        type=float,
        default=1e-4,
        help="Learning rate to use.",
    )
    parser.add_argument("--weight_decay", type=float, default=1e-2, help="Weight decay to use.")
    parser.add_argument("--num_train_epochs", type=int, default=100)
    parser.add_argument(
        "--max_train_steps",
        type=int,
        default=None,
        help="Total number of training steps to perform. If provided, overrides num_train_epochs.",
    )
    parser.add_argument(
        "--train_batch_size", type=int, default=8, help="Batch size (per device) for the training dataloader."
    )
    parser.add_argument(
        "--dataloader_num_workers",
        type=int,
        default=4,
        help="Number of subprocesses to use for data loading.",
    )
    parser.add_argument(
        "--save_steps",
        type=int,
        default=2000,
        help="Save a checkpoint of the training state every X updates",
    )
    parser.add_argument(
        "--mixed_precision",
        type=str,
        default="fp16",
        choices=["no", "fp16", "bf16"],
        help="Whether to use mixed precision training.",
    )
    parser.add_argument(
        "--report_to",
        type=str,
        default="tensorboard",
        help="The integration to report the results and logs to.",
    )
    parser.add_argument(
        "--resume_from_checkpoint",
        type=str,
        default=None,
        help="If the training should continue from a checkpoint folder.",
    )
    parser.add_argument(
        "--i_drop_rate",
        type=float,
        default=0.1,
        help="Dropout rate for condition image.",
    )
    parser.add_argument(
        "--phase",
        type=str,
        default="train",
        choices=["train", "test"],
        help="Training phase.",
    )
    parser.add_argument(
        "--order",
        type=str,
        default="paired",
        choices=["paired", "unpaired"],
        help="Data order for training.",
    )
    parser.add_argument(
        "--blur_mask",
        action="store_true",
        default=True,
        help="Enable mask blurring for smooth edges (CatVTON style).",
    )
    parser.add_argument(
        "--blur_radius",
        type=int,
        default=9,
        help="Gaussian blur radius for mask smoothing (CatVTON uses 9).",
    )
    
    args = parser.parse_args()
    return args


def main():
    args = parse_args()
    logging_dir = Path(args.output_dir, args.logging_dir)

    accelerator_project_config = ProjectConfiguration(project_dir=args.output_dir, logging_dir=logging_dir)

    accelerator = Accelerator(
        mixed_precision=args.mixed_precision,
        log_with=args.report_to,
        project_config=accelerator_project_config,
    )
    
    if accelerator.is_main_process:
        if args.output_dir is not None:
            os.makedirs(args.output_dir, exist_ok=True)

    # Load scheduler, tokenizer and models.
    noise_scheduler = DDPMScheduler.from_pretrained(args.pretrained_model_name_or_path, subfolder="scheduler")
    vae = AutoencoderKL.from_pretrained(args.pretrained_model_name_or_path, subfolder="vae")
    unet = UNet2DConditionModel.from_pretrained(args.pretrained_model_name_or_path, subfolder="unet")
    
    # freeze parameters of models to save more memory
    unet.requires_grad_(False)
    vae.requires_grad_(False)
    
    # init adapter modules
    attn_procs = {}
    for name in unet.attn_processors.keys():
        cross_attention_dim = None if name.endswith("attn1.processor") else unet.config.cross_attention_dim
        if name.startswith("mid_block"):
            hidden_size = unet.config.block_out_channels[-1]
        elif name.startswith("up_blocks"):
            block_id = int(name[len("up_blocks.")])
            hidden_size = list(reversed(unet.config.block_out_channels))[block_id]
        elif name.startswith("down_blocks"):
            block_id = int(name[len("down_blocks.")])
            hidden_size = unet.config.block_out_channels[block_id]
        
        if cross_attention_dim is None:
            attn_procs[name] = AttnProcessor2_0(hidden_size=hidden_size, cross_attention_dim=cross_attention_dim)
        else:
            attn_procs[name] = SkipAttnProcessor(hidden_size=hidden_size, cross_attention_dim=cross_attention_dim)

    unet.set_attn_processor(attn_procs)
    
    # Only train attn1 parameters
    for name, param in unet.named_parameters():
        if 'attn1' in name:
            param.requires_grad_(True)
    
    attn_blocks = torch.nn.ModuleList()
    for name, param in unet.named_modules():
        if "attn1" in name:
            attn_blocks.append(param)
            
    # Load from checkpoint if provided
    if args.resume_from_checkpoint is not None:        
        sd_state = torch.load(args.resume_from_checkpoint, map_location="cpu", weights_only=True)
        attn_sd = {}
        for k in sd_state:
            if 'attn_block' in k:
                attn_sd[k.replace('attn_block.', '')] = sd_state[k]
        attn_blocks.load_state_dict(attn_sd)
   
    lem = LEM(unet, attn_blocks)
    
    weight_dtype = torch.float32
    if accelerator.mixed_precision == "fp16":
        weight_dtype = torch.float16
    elif accelerator.mixed_precision == "bf16":
        weight_dtype = torch.bfloat16
    
    vae.to(accelerator.device, dtype=weight_dtype)
    
    # optimizer
    params_to_opt = itertools.chain(lem.attn_block.parameters())
    optimizer = torch.optim.AdamW(params_to_opt, lr=args.learning_rate, weight_decay=args.weight_decay)
    
    # Dataset and DataLoader
    train_dataset = VTONLEMDatasetCatVTONStyle(
        dataroot_path=args.data_root_path,
        phase=args.phase,
        order=args.order,
        size=(args.height, args.width),
        i_drop_rate=args.i_drop_rate,
        args=args,
        blur_mask=args.blur_mask,      # 启用掩码平滑处理
        blur_radius=args.blur_radius   # 与CatVTON一致的模糊半径
    )
    
    train_dataloader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=args.train_batch_size,
        shuffle=True,
        collate_fn=collate_fn,
        num_workers=args.dataloader_num_workers,
    )
    
    # Prepare everything with our `accelerator`.
    lem, optimizer, train_dataloader = accelerator.prepare(
        lem, optimizer, train_dataloader
    )
    
    # Train!
    total_batch_size = args.train_batch_size * accelerator.num_processes

    # 计算训练步数
    if args.max_train_steps is None:
        args.max_train_steps = args.num_train_epochs * len(train_dataloader)
    else:
        # 如果指定了max_train_steps，重新计算epochs
        args.num_train_epochs = math.ceil(args.max_train_steps / len(train_dataloader))

    logger.info("***** Running training *****")
    logger.info(f"  Num examples = {len(train_dataset)}")
    logger.info(f"  Num Epochs = {args.num_train_epochs}")
    logger.info(f"  Instantaneous batch size per device = {args.train_batch_size}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {total_batch_size}")
    logger.info(f"  Total optimization steps = {args.max_train_steps}")

    global_step = 0

    # 创建进度条（仅在主进程显示）
    if accelerator.is_main_process:
        progress_bar = tqdm(
            total=args.max_train_steps,
            desc="🚀 Training Progress",
            unit="step",
            ncols=100,
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}] Loss: {postfix}"
        )

    for epoch in range(args.num_train_epochs):
        lem.train()
        for step, batch in enumerate(train_dataloader):
            with accelerator.accumulate(lem):
                # Prepare image data
                concat_dim = -2  # Y axis concat (same as original LEM)
                image, condition_image, mask = batch['cloth'], batch['logo'], batch['mask']
                image, condition_image, mask = check_inputs(image, condition_image, mask, args.width, args.height)
                
                image = prepare_image(image).to(accelerator.device, dtype=weight_dtype)
                condition_image = prepare_image(condition_image).to(accelerator.device, dtype=weight_dtype)
                mask = prepare_mask_image(mask).to(accelerator.device, dtype=weight_dtype)
                
                # Mask image
                masked_image = image * (mask < 0.5)
                
                # VAE encoding
                image_latent = compute_vae_encodings(image, vae)
                masked_latent = compute_vae_encodings(masked_image, vae)
                condition_latent = compute_vae_encodings(condition_image, vae)
                
                # Apply dropout to condition
                image_embeds_ = []
                for image_embed, drop_image_embed in zip(condition_latent, batch["drop_image_embed"]):
                    if drop_image_embed == 1:
                        image_embeds_.append(torch.zeros_like(image_embed))
                    else:
                        image_embeds_.append(image_embed)
                image_embeds = torch.stack(image_embeds_)
                condition_latent = image_embeds
                
                mask_latent = torch.nn.functional.interpolate(mask, size=masked_latent.shape[-2:], mode="nearest")
                
                # Concatenate latents
                masked_latent_concat = torch.cat([masked_latent, condition_latent], dim=concat_dim)
                mask_latent_concat = torch.cat([mask_latent, torch.zeros_like(mask_latent)], dim=concat_dim)
                
                # Ground truth
                gt = batch['gt']
                gt = prepare_image(gt).to(accelerator.device, dtype=weight_dtype)
                gt_latent = compute_vae_encodings(gt, vae)
                image_latent_concat = torch.cat([gt_latent, condition_latent], dim=concat_dim)
                
                # Sample noise
                noise = torch.randn_like(masked_latent_concat)
                bsz = masked_latent_concat.shape[0]
                timesteps = torch.randint(0, noise_scheduler.num_train_timesteps, (bsz,), device=masked_latent_concat.device)
                timesteps = timesteps.long()

                # Add noise to the latents
                noisy_latents = noise_scheduler.add_noise(image_latent_concat, noise, timesteps)
                
                # Concat in channel dimension for inpainting
                inpainting_latent_model_input = torch.cat([noisy_latents, mask_latent_concat, masked_latent_concat], dim=1)
                
                # Generate CLIP GT features using multimodal model
                clip_gt_features = None
                if hasattr(batch, 'person') and hasattr(batch, 'cloth'):
                    # Use multimodal model to predict try-on result
                    multimodal_predictor = MultimodalTryOnPredictor()
                    predicted_description = multimodal_predictor.predict_tryOn_result(
                        batch['person'], batch['cloth']
                    )
                    clip_gt_features = multimodal_predictor.extract_clip_features(
                        predicted_description, reference_images=batch.get('gt')
                    )

                # Predict noise with CLIP semantic enhancement
                noise_pred = lem(inpainting_latent_model_input, timesteps, clip_gt_features)

                # Compute loss
                loss = F.mse_loss(noise_pred.float(), noise.float(), reduction="mean")

                # Backpropagate
                accelerator.backward(loss)
                optimizer.step()
                optimizer.zero_grad()

            # Logging
            if accelerator.is_main_process:
                if global_step % 100 == 0:
                    progress_pct = (global_step / args.max_train_steps) * 100
                    logger.info(f"Epoch {epoch}, Step {global_step}/{args.max_train_steps} ({progress_pct:.1f}%), Loss: {loss.item():.4f}")

                # 每1000步详细报告
                if global_step % 1000 == 0:
                    logger.info(f"🚀 Training Progress: {global_step}/{args.max_train_steps} steps completed ({progress_pct:.2f}%)")
                
                # Save checkpoint (定期保存)
                if global_step % args.save_steps == 0:
                    save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                    accelerator.save_state(save_path)

                    # Save attention blocks in CatVTON format
                    # 在分布式训练中，需要使用 .module 访问原始模型
                    attn_block = lem.module.attn_block if hasattr(lem, 'module') else lem.attn_block
                    save_catvton_format_weights(attn_block, save_path, logger)
                    logger.info(f"Saved regular checkpoint to {save_path}")

                # Save milestone checkpoints (重要节点保存)
                milestone_steps = [1000, 5000, 10000, 20000, 50000, 100000, 150000, 200000]
                if global_step in milestone_steps:
                    milestone_path = os.path.join(args.output_dir, f"milestone-{global_step}")
                    accelerator.save_state(milestone_path)
                    # 在分布式训练中，需要使用 .module 访问原始模型
                    attn_block = lem.module.attn_block if hasattr(lem, 'module') else lem.attn_block
                    save_catvton_format_weights(attn_block, milestone_path, logger)
                    logger.info(f"🎯 Saved MILESTONE checkpoint to {milestone_path}")

            global_step += 1

            # 更新进度条
            if accelerator.is_main_process:
                progress_bar.update(1)
                progress_bar.set_postfix_str(f"{loss.item():.4f}")

            # 检查是否达到最大训练步数
            if global_step >= args.max_train_steps:
                if accelerator.is_main_process:
                    progress_bar.close()
                logger.info(f"Reached max_train_steps ({args.max_train_steps}), stopping training...")
                break

        # 如果达到最大步数，跳出epoch循环
        if global_step >= args.max_train_steps:
            break

    # 关闭进度条
    if accelerator.is_main_process:
        progress_bar.close()

    # Save final model
    if accelerator.is_main_process:
        save_path = os.path.join(args.output_dir, "final_model")
        accelerator.save_state(save_path)

        # Save final attention blocks in CatVTON format
        # 在分布式训练中，需要使用 .module 访问原始模型
        attn_block = lem.module.attn_block if hasattr(lem, 'module') else lem.attn_block
        save_catvton_format_weights(attn_block, save_path, logger)
        logger.info(f"Training completed! Final model saved to {save_path}")


if __name__ == "__main__":
    main() 