# Python CircleCI 2.0 configuration file
#
# Check https://circleci.com/docs/2.0/language-python/ for more details
#
version: 2

# -------------------------------------------------------------------------------------
# Environments to run the jobs in
# -------------------------------------------------------------------------------------
cpu: &cpu
  docker:
    - image: circleci/python:3.6.8-stretch
  resource_class: medium

gpu: &gpu
  machine:
    image: ubuntu-1604:201903-01
    docker_layer_caching: true
  resource_class: gpu.small

# -------------------------------------------------------------------------------------
# Re-usable commands
# -------------------------------------------------------------------------------------
install_python: &install_python
  - run:
      name: Install Python
      working_directory: ~/
      command: |
        pyenv install 3.6.1
        pyenv global 3.6.1

setup_venv: &setup_venv
  - run:
      name: Setup Virtual Env
      working_directory: ~/
      command: |
        python -m venv ~/venv
        echo ". ~/venv/bin/activate" >> $BASH_ENV
        . ~/venv/bin/activate
        python --version
        which python
        which pip
        pip install --upgrade pip

install_dep: &install_dep
  - run:
      name: Install Dependencies
      command: |
        pip install --progress-bar off -U 'git+https://github.com/facebookresearch/fvcore'
        pip install --progress-bar off cython opencv-python
        pip install --progress-bar off 'git+https://github.com/cocodataset/cocoapi.git#subdirectory=PythonAPI'
        pip install --progress-bar off torch torchvision

install_detectron2: &install_detectron2
  - run:
      name: Install Detectron2
      command: |
        gcc --version
        pip install -U --progress-bar off -e .[dev]
        python -m detectron2.utils.collect_env

install_nvidia_driver: &install_nvidia_driver
  - run:
      name: Install nvidia driver
      working_directory: ~/
      command: |
        wget -q 'https://s3.amazonaws.com/ossci-linux/nvidia_driver/NVIDIA-Linux-x86_64-430.40.run'
        sudo /bin/bash ./NVIDIA-Linux-x86_64-430.40.run -s --no-drm
        nvidia-smi

run_unittests: &run_unittests
  - run:
      name: Run Unit Tests
      command: |
        python -m unittest discover -v -s tests

# -------------------------------------------------------------------------------------
# Jobs to run
# -------------------------------------------------------------------------------------
jobs:
  cpu_tests:
    <<: *cpu

    working_directory: ~/detectron2

    steps:
      - checkout
      - <<: *setup_venv

      # Cache the venv directory that contains dependencies
      - restore_cache:
          keys:
            - cache-key-{{ .Branch }}-***********

      - <<: *install_dep

      - save_cache:
          paths:
            - ~/venv
          key: cache-key-{{ .Branch }}-***********

      - <<: *install_detectron2

      - run:
          name: isort
          command: |
            isort -c -sp .
      - run:
          name: black
          command: |
            black --check -l 100 .
      - run:
          name: flake8
          command: |
            flake8 .

      - <<: *run_unittests

  gpu_tests:
    <<: *gpu

    working_directory: ~/detectron2

    steps:
      - checkout
      - <<: *install_nvidia_driver

      - run:
          name: Install nvidia-docker
          working_directory: ~/
          command: |
            curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
            distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
            curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | \
            sudo tee /etc/apt/sources.list.d/nvidia-docker.list
            sudo apt-get update && sudo apt-get install -y nvidia-docker2
            # reload the docker daemon configuration
            sudo pkill -SIGHUP dockerd

      - run:
          name: Launch docker
          working_directory: ~/detectron2/docker
          command: |
            nvidia-docker build -t detectron2:v0 -f Dockerfile-circleci .
            nvidia-docker run -itd --name d2 detectron2:v0
            docker exec -it d2 nvidia-smi

      - run:
          name: Build Detectron2
          command: |
            docker exec -it d2 pip install 'git+https://github.com/facebookresearch/fvcore'
            docker cp ~/detectron2 d2:/detectron2
            # This will build d2 for the target GPU arch only
            docker exec -it d2 pip install -e /detectron2
            docker exec -it d2 python3 -m detectron2.utils.collect_env
            docker exec -it d2 python3 -c 'import torch; assert(torch.cuda.is_available())'

      - run:
          name: Run Unit Tests
          command: |
            docker exec -e CIRCLECI=true -it d2 python3 -m unittest discover -v -s /detectron2/tests

workflows:
  version: 2
  regular_test:
    jobs:
      - cpu_tests
      - gpu_tests

  #nightly_test:
    #jobs:
      #- gpu_tests
    #triggers:
      #- schedule:
          #cron: "0 0 * * *"
          #filters:
            #branches:
              #only:
                #- master
