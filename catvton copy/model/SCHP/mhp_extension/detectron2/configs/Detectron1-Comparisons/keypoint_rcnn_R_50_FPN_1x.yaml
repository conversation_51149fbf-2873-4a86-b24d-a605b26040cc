_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-50.pkl"
  KEYPOINT_ON: True
  RESNETS:
    DEPTH: 50
  ROI_HEADS:
    NUM_CLASSES: 1
  ROI_KEYPOINT_HEAD:
    POOLER_RESOLUTION: 14
    POOLER_SAMPLING_RATIO: 2
    POOLER_TYPE: "ROIAlign"
  # Detectron1 uses smooth L1 loss with some magic beta values.
  # The defaults are changed to L1 loss in Detectron2.
  ROI_BOX_HEAD:
    SMOOTH_L1_BETA: 1.0
    POOLER_SAMPLING_RATIO: 2
    POOLER_TYPE: "ROIAlign"
  RPN:
    SMOOTH_L1_BETA: 0.1111
    # Detectron1 uses 2000 proposals per-batch, but this option is per-image in detectron2
    # 1000 proposals per-image is found to hurt box AP.
    # Therefore we increase it to 1500 per-image.
    POST_NMS_TOPK_TRAIN: 1500
DATASETS:
  TRAIN: ("keypoints_coco_2017_train",)
  TEST: ("keypoints_coco_2017_val",)
