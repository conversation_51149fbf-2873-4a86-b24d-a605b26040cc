_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  META_ARCHITECTURE: "PanopticFPN"
  WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-50.pkl"
  MASK_ON: True
  RESNETS:
    DEPTH: 50
  SEM_SEG_HEAD:
    LOSS_WEIGHT: 0.5
DATASETS:
  TRAIN: ("coco_2017_val_100_panoptic_separated",)
  TEST: ("coco_2017_val_100_panoptic_separated",)
SOLVER:
  BASE_LR: 0.005
  STEPS: (30,)
  MAX_ITER: 40
  IMS_PER_BATCH: 4
DATALOADER:
  NUM_WORKERS: 1
