#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
from diffusers import UNet2DConditionModel

def analyze_cross_attention():
    """
    详细分析Cross Attention的维度配置
    """
    print("🔍 分析Cross Attention维度...")
    
    model_path = "/home2/wangyuanpeng/.cache/huggingface/hub/models--runwayml--stable-diffusion-inpainting/snapshots/8a4288a76071f7280aedbdb3253bdb9e9d5d84bb"
    
    try:
        unet = UNet2DConditionModel.from_pretrained(model_path, subfolder="unet")
        
        print(f"📊 Cross Attention配置:")
        print(f"  - cross_attention_dim: {unet.config.cross_attention_dim}")
        print(f"  - attention_head_dim: {unet.config.attention_head_dim}")
        
        print(f"\n🎯 Cross Attention (attn2) 维度分析:")
        
        self_attn_count = 0
        cross_attn_count = 0
        
        for name in unet.attn_processors.keys():
            # 判断是self attention还是cross attention
            is_self_attn = name.endswith("attn1.processor")
            is_cross_attn = name.endswith("attn2.processor")
            
            if is_cross_attn:
                cross_attn_count += 1
                
                # 计算hidden_size
                if name.startswith("mid_block"):
                    hidden_size = unet.config.block_out_channels[-1]
                    block_type = "mid_block"
                elif name.startswith("up_blocks"):
                    block_id = int(name[len("up_blocks.")])
                    hidden_size = list(reversed(unet.config.block_out_channels))[block_id]
                    block_type = f"up_blocks.{block_id}"
                elif name.startswith("down_blocks"):
                    block_id = int(name[len("down_blocks.")])
                    hidden_size = unet.config.block_out_channels[block_id]
                    block_type = f"down_blocks.{block_id}"
                
                # 计算注意力头数
                num_heads = hidden_size // unet.config.attention_head_dim
                
                print(f"  - {name}:")
                print(f"    * hidden_size: {hidden_size}")
                print(f"    * cross_attention_dim: {unet.config.cross_attention_dim}")
                print(f"    * attention_heads: {num_heads}")
                print(f"    * block_type: {block_type}")
                
            elif is_self_attn:
                self_attn_count += 1
        
        print(f"\n📈 统计信息:")
        print(f"  - Self Attention层数: {self_attn_count}")
        print(f"  - Cross Attention层数: {cross_attn_count}")
        print(f"  - 总Attention层数: {self_attn_count + cross_attn_count}")
        
        # 分析Cross Attention的参数量
        cross_attn_params = 0
        for name, param in unet.named_parameters():
            if 'attn2' in name:  # Cross attention
                cross_attn_params += param.numel()
        
        print(f"\n💾 Cross Attention参数量:")
        print(f"  - Cross Attention参数量: {cross_attn_params:,}")
        
        # 按维度分组统计
        print(f"\n📊 按Hidden Size分组的Cross Attention:")
        dim_groups = {}
        for name in unet.attn_processors.keys():
            if name.endswith("attn2.processor"):
                if name.startswith("mid_block"):
                    hidden_size = unet.config.block_out_channels[-1]
                elif name.startswith("up_blocks"):
                    block_id = int(name[len("up_blocks.")])
                    hidden_size = list(reversed(unet.config.block_out_channels))[block_id]
                elif name.startswith("down_blocks"):
                    block_id = int(name[len("down_blocks.")])
                    hidden_size = unet.config.block_out_channels[block_id]
                
                if hidden_size not in dim_groups:
                    dim_groups[hidden_size] = 0
                dim_groups[hidden_size] += 1
        
        for hidden_size, count in sorted(dim_groups.items()):
            num_heads = hidden_size // unet.config.attention_head_dim
            print(f"  - Hidden Size {hidden_size}: {count}层, {num_heads}个注意力头")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    analyze_cross_attention()
