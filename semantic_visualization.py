#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
语义图可视化工具
用于将人物图像的不同区域用不同颜色的语义图展示
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from PIL import Image, ImageDraw, ImageFont
import argparse
import os
from typing import Dict, List, Tuple, Optional

class SemanticVisualizer:
    """语义图可视化器"""
    
    def __init__(self):
        # 定义语义区域和对应的颜色
        self.semantic_regions = {
            'hair': {'color': (255, 0, 0), 'name': '头发'},      # 红色
            'face': {'color': (0, 0, 255), 'name': '面部'},      # 蓝色  
            'neck': {'color': (139, 69, 19), 'name': '颈部'},    # 棕色
            'upper_body': {'color': (255, 165, 0), 'name': '上身'}, # 橙色
            'arms': {'color': (0, 255, 255), 'name': '手臂'},    # 青色
            'hands': {'color': (0, 128, 128), 'name': '手部'},   # 深青色
            'lower_body': {'color': (0, 100, 0), 'name': '下身'}, # 深绿色
            'legs': {'color': (135, 206, 235), 'name': '腿部'},  # 天蓝色
            'background': {'color': (0, 0, 0), 'name': '背景'}   # 黑色
        }
        
        # 创建颜色映射
        self.color_map = plt.cm.get_cmap('tab10')
        
    def create_semantic_mask_from_image(self, image_path: str) -> np.ndarray:
        """
        从输入图像创建语义掩码
        这里使用简单的颜色分割方法，实际应用中可以使用更复杂的分割模型
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
            
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        height, width = image_rgb.shape[:2]
        
        # 创建语义掩码
        semantic_mask = np.zeros((height, width), dtype=np.uint8)
        
        # 使用HSV颜色空间进行分割
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # 定义各个区域的HSV范围（这里是示例，实际需要根据具体图像调整）
        regions = {
            1: {'name': 'hair', 'hsv_range': [(0, 50, 50), (10, 255, 255)]},      # 红色区域 -> 头发
            2: {'name': 'face', 'hsv_range': [(100, 50, 50), (130, 255, 255)]},   # 蓝色区域 -> 面部
            3: {'name': 'neck', 'hsv_range': [(10, 50, 50), (25, 255, 255)]},     # 棕色区域 -> 颈部
            4: {'name': 'upper_body', 'hsv_range': [(15, 50, 50), (35, 255, 255)]}, # 橙色区域 -> 上身
            5: {'name': 'arms', 'hsv_range': [(80, 50, 50), (100, 255, 255)]},    # 青色区域 -> 手臂
            6: {'name': 'lower_body', 'hsv_range': [(40, 50, 50), (80, 255, 255)]}, # 绿色区域 -> 下身
        }
        
        # 为每个区域创建掩码
        for region_id, region_info in regions.items():
            lower = np.array(region_info['hsv_range'][0])
            upper = np.array(region_info['hsv_range'][1])
            mask = cv2.inRange(hsv, lower, upper)
            semantic_mask[mask > 0] = region_id
            
        return semantic_mask, image_rgb
    
    def create_semantic_visualization(self, semantic_mask: np.ndarray, 
                                    original_image: np.ndarray,
                                    alpha: float = 0.6) -> np.ndarray:
        """
        创建语义图可视化
        
        Args:
            semantic_mask: 语义掩码，每个像素值代表不同的语义类别
            original_image: 原始图像
            alpha: 透明度，控制语义图与原图的混合程度
        """
        height, width = semantic_mask.shape
        
        # 创建彩色语义图
        semantic_colored = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 为每个语义类别分配颜色
        unique_labels = np.unique(semantic_mask)
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
        
        for i, label in enumerate(unique_labels):
            if label == 0:  # 背景
                continue
            mask = semantic_mask == label
            color = (colors[i][:3] * 255).astype(np.uint8)
            semantic_colored[mask] = color
            
        # 混合原图和语义图
        if original_image.shape[:2] != semantic_mask.shape:
            original_image = cv2.resize(original_image, (width, height))
            
        blended = cv2.addWeighted(original_image, 1-alpha, semantic_colored, alpha, 0)
        
        return blended, semantic_colored
    
    def create_legend(self, semantic_mask: np.ndarray, 
                     output_size: Tuple[int, int] = (200, 400)) -> np.ndarray:
        """创建语义图例"""
        unique_labels = np.unique(semantic_mask)
        colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
        
        # 创建图例图像
        legend_img = np.ones((output_size[1], output_size[0], 3), dtype=np.uint8) * 255
        
        # 语义类别名称
        label_names = {
            0: '背景',
            1: '头发', 
            2: '面部',
            3: '颈部',
            4: '上身',
            5: '手臂', 
            6: '下身'
        }
        
        # 绘制图例
        legend_pil = Image.fromarray(legend_img)
        draw = ImageDraw.Draw(legend_pil)
        
        try:
            # 尝试使用中文字体
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        y_offset = 20
        for i, label in enumerate(unique_labels):
            if label == 0:
                continue
                
            color = (colors[i][:3] * 255).astype(int)
            color_tuple = tuple(color)
            
            # 绘制颜色块
            draw.rectangle([10, y_offset, 40, y_offset + 20], fill=color_tuple)
            
            # 绘制文字
            label_name = label_names.get(label, f'区域{label}')
            draw.text((50, y_offset + 2), label_name, fill=(0, 0, 0), font=font)
            
            y_offset += 30
            
        return np.array(legend_pil)
    
    def visualize_semantic_image(self, image_path: str, 
                               output_path: Optional[str] = None,
                               show_legend: bool = True,
                               alpha: float = 0.6) -> None:
        """
        完整的语义图可视化流程
        
        Args:
            image_path: 输入图像路径
            output_path: 输出路径，如果为None则显示图像
            show_legend: 是否显示图例
            alpha: 语义图透明度
        """
        # 创建语义掩码
        semantic_mask, original_image = self.create_semantic_mask_from_image(image_path)
        
        # 创建可视化
        blended, semantic_colored = self.create_semantic_visualization(
            semantic_mask, original_image, alpha
        )
        
        # 创建图例
        if show_legend:
            legend = self.create_legend(semantic_mask)
        
        # 创建组合图像
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('语义图可视化结果', fontsize=16)
        
        # 原图
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title('原始图像')
        axes[0, 0].axis('off')
        
        # 纯语义图
        axes[0, 1].imshow(semantic_colored)
        axes[0, 1].set_title('语义分割图')
        axes[0, 1].axis('off')
        
        # 混合图
        axes[1, 0].imshow(blended)
        axes[1, 0].set_title(f'混合图像 (α={alpha})')
        axes[1, 0].axis('off')
        
        # 图例
        if show_legend:
            axes[1, 1].imshow(legend)
            axes[1, 1].set_title('语义图例')
            axes[1, 1].axis('off')
        else:
            axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        if output_path:
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            print(f"语义图可视化结果已保存到: {output_path}")
        else:
            plt.show()
            
        plt.close()

def main():
    parser = argparse.ArgumentParser(description='语义图可视化工具')
    parser.add_argument('--input', '-i', type=str, required=True, 
                       help='输入图像路径')
    parser.add_argument('--output', '-o', type=str, default=None,
                       help='输出图像路径')
    parser.add_argument('--alpha', '-a', type=float, default=0.6,
                       help='语义图透明度 (0-1)')
    parser.add_argument('--no-legend', action='store_true',
                       help='不显示图例')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        return
    
    # 创建可视化器
    visualizer = SemanticVisualizer()
    
    # 生成语义图可视化
    visualizer.visualize_semantic_image(
        image_path=args.input,
        output_path=args.output,
        show_legend=not args.no_legend,
        alpha=args.alpha
    )

if __name__ == "__main__":
    main()
