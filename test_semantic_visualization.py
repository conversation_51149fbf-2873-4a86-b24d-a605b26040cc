#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
语义可视化测试脚本
演示如何使用语义图可视化工具
"""

import os
import numpy as np
from PIL import Image, ImageDraw
import matplotlib.pyplot as plt
from advanced_semantic_visualizer import AdvancedSemanticVisualizer

def create_sample_semantic_image(output_path: str = "sample_semantic_image.png"):
    """
    创建一个示例的语义分割图像，类似于用户提供的图像
    """
    # 创建一个512x768的图像（类似人物比例）
    width, height = 512, 768
    image = Image.new('RGB', (width, height), (0, 0, 0))  # 黑色背景
    draw = ImageDraw.Draw(image)
    
    # 定义不同区域的颜色和形状
    regions = [
        # 头发区域 (红色)
        {'color': (255, 0, 0), 'shape': 'ellipse', 'coords': [150, 50, 350, 200]},
        
        # 面部区域 (蓝色) 
        {'color': (0, 0, 255), 'shape': 'ellipse', 'coords': [180, 120, 320, 250]},
        
        # 颈部区域 (棕色)
        {'color': (139, 69, 19), 'shape': 'rectangle', 'coords': [220, 240, 280, 300]},
        
        # 上身区域 (橙色)
        {'color': (255, 165, 0), 'shape': 'rectangle', 'coords': [120, 300, 380, 500]},
        
        # 左臂区域 (青色)
        {'color': (0, 255, 255), 'shape': 'ellipse', 'coords': [50, 350, 130, 600]},
        
        # 右臂区域 (天蓝色)
        {'color': (135, 206, 235), 'shape': 'ellipse', 'coords': [370, 350, 450, 600]},
        
        # 下身区域 (深绿色)
        {'color': (0, 100, 0), 'shape': 'rectangle', 'coords': [150, 500, 350, 700]},
    ]
    
    # 绘制各个区域
    for region in regions:
        color = region['color']
        coords = region['coords']
        
        if region['shape'] == 'ellipse':
            draw.ellipse(coords, fill=color)
        elif region['shape'] == 'rectangle':
            draw.rectangle(coords, fill=color)
    
    # 保存图像
    image.save(output_path)
    print(f"示例语义图像已创建: {output_path}")
    return output_path

def test_visualization():
    """测试语义可视化功能"""
    print("=== 语义图可视化测试 ===")
    
    # 创建示例图像
    sample_image_path = create_sample_semantic_image()
    
    # 创建可视化器
    visualizer = AdvancedSemanticVisualizer()
    
    # 生成可视化结果
    output_path = "semantic_visualization_result.png"
    
    print("\n正在生成语义可视化...")
    visualizer.create_comprehensive_visualization(
        image_path=sample_image_path,
        output_path=output_path,
        n_colors=8,
        alpha=0.6
    )
    
    print(f"\n可视化完成！结果保存在: {output_path}")
    
    # 清理临时文件
    if os.path.exists(sample_image_path):
        os.remove(sample_image_path)
        print(f"临时文件已清理: {sample_image_path}")

def test_with_real_image(image_path: str):
    """使用真实图像进行测试"""
    if not os.path.exists(image_path):
        print(f"错误: 图像文件不存在: {image_path}")
        return
    
    print(f"=== 使用真实图像测试: {image_path} ===")
    
    # 创建可视化器
    visualizer = AdvancedSemanticVisualizer()
    
    # 生成可视化结果
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    output_path = f"{base_name}_semantic_analysis.png"
    
    print("\n正在分析图像并生成可视化...")
    visualizer.create_comprehensive_visualization(
        image_path=image_path,
        output_path=output_path,
        n_colors=8,
        alpha=0.6
    )
    
    print(f"\n分析完成！结果保存在: {output_path}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='语义可视化测试工具')
    parser.add_argument('--image', '-i', type=str, default=None,
                       help='要分析的图像路径（可选）')
    parser.add_argument('--demo', action='store_true',
                       help='运行演示模式，使用生成的示例图像')
    
    args = parser.parse_args()
    
    if args.demo or args.image is None:
        # 运行演示
        test_visualization()
    
    if args.image:
        # 使用用户提供的图像
        test_with_real_image(args.image)

if __name__ == "__main__":
    main()
